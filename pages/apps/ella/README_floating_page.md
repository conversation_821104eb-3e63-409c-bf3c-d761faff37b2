# Ella浮窗页面 (EllaFloatingPage)

## 概述

`EllaFloatingPage` 是一个专门为 Ella 语音助手浮窗模式设计的独立页面封装类。它提供了完整的浮窗管理、命令执行和响应处理功能。

## 主要特性

### 🎯 浮窗管理
- **长按Power键唤起**: 模拟长按power键唤起Ella浮窗（主要方法）
- **多种打开方式**: 支持shell命令、广播、按键组合等方式打开浮窗
- **关闭浮窗**: 通过按钮、手势或广播关闭浮窗
- **移动浮窗**: 拖拽浮窗到指定位置
- **最小化/展开**: 控制浮窗的显示状态

### 💬 命令执行
- **文本命令**: 在浮窗中输入并执行文本命令
- **语音命令**: 支持语音输入（可转换为文本输入）
- **输入框管理**: 自动检测和使用可用的输入框

### 📝 响应处理
- **等待响应**: 智能等待AI响应
- **获取响应**: 从多个源获取响应文本
- **批量获取**: 获取所有响应文本内容

### 🔍 状态检查
- **可见性检查**: 检查浮窗是否可见
- **就绪状态**: 确保浮窗和输入框就绪
- **位置信息**: 获取浮窗的位置和大小

## 页面元素

### 核心元素
```python
# 浮窗容器
self.floating_container

# 输入相关
self.floating_input_box        # 主输入框
self.floating_text_input       # 备选输入框
self.floating_send_button      # 发送按钮
self.floating_voice_button     # 语音按钮

# 控制按钮
self.floating_close_button     # 关闭按钮
self.floating_minimize_button  # 最小化按钮
self.floating_expand_button    # 展开按钮

# 内容区域
self.floating_response_area    # 响应区域
self.floating_chat_list        # 聊天列表
self.floating_drag_area        # 拖拽区域
```

## 使用方法

### 基本使用

```python
from pages.apps.ella.floating_page import EllaFloatingPage

# 初始化浮窗页面
floating_page = EllaFloatingPage()

# 方法1: 通过长按power键唤起浮窗（推荐）
if floating_page.trigger_ella_by_power_key(duration=3.0):
    print("通过长按power键成功唤起浮窗")
else:
    # 方法2: 备选方案 - 使用其他方式打开浮窗
    if floating_page.open_floating_window():
        print("通过备选方法打开浮窗成功")

# 确保浮窗就绪
if floating_page.ensure_floating_window_ready():
    print("浮窗已就绪")

# 执行文本命令
command = "What's the weather today?"
if floating_page.execute_text_command_in_floating(command):
    # 等待响应
    if floating_page.wait_for_floating_response():
        response = floating_page.get_floating_response_text()
        print(f"AI响应: {response}")

# 关闭浮窗
floating_page.close_floating_window()
```

### 高级操作

```python
# 测试不同长按时长唤起浮窗
durations = [2.0, 3.0, 4.0, 5.0]
for duration in durations:
    if floating_page.trigger_ella_by_power_key(duration=duration):
        print(f"长按{duration}秒成功唤起浮窗")
        break

# 移动浮窗到指定位置
floating_page.move_floating_window(500, 300)

# 最小化浮窗
floating_page.minimize_floating_window()

# 展开浮窗
floating_page.expand_floating_window()

# 检查浮窗状态
status = floating_page.check_floating_window_status()
print(f"浮窗状态: {status}")

# 获取所有响应文本
all_texts = floating_page.get_all_floating_response_texts()
print(f"所有响应: {all_texts}")
```

## 方法说明

### 浮窗管理方法

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| `trigger_ella_by_power_key(duration)` | 通过长按power键唤起浮窗 | duration: float | bool |
| `open_floating_window()` | 打开Ella浮窗（多种方式） | - | bool |
| `close_floating_window()` | 关闭Ella浮窗 | - | bool |
| `is_floating_window_visible()` | 检查浮窗是否可见 | - | bool |
| `minimize_floating_window()` | 最小化浮窗 | - | bool |
| `expand_floating_window()` | 展开浮窗 | - | bool |
| `move_floating_window(x, y)` | 移动浮窗到指定位置 | x: int, y: int | bool |

### 命令执行方法

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| `execute_text_command_in_floating(command)` | 执行文本命令 | command: str | bool |
| `execute_voice_command_in_floating(command, duration, language)` | 执行语音命令 | command: str, duration: float, language: str | bool |

### 响应处理方法

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| `wait_for_floating_response(timeout)` | 等待AI响应 | timeout: int | bool |
| `get_floating_response_text()` | 获取响应文本 | - | str |
| `get_all_floating_response_texts()` | 获取所有响应文本 | - | list |

### 状态检查方法

| 方法 | 说明 | 返回值 |
|------|------|--------|
| `check_floating_window_status()` | 检查浮窗状态 | dict |
| `ensure_floating_window_ready()` | 确保浮窗就绪 | bool |

## 示例代码

完整的使用示例请参考：`examples/ella_floating_page_example.py`

## 注意事项

1. **浮窗权限**: 确保应用有浮窗显示权限
2. **元素定位**: 浮窗元素的resourceId可能因版本而异，需要根据实际情况调整
3. **状态检查**: 在执行操作前建议先检查浮窗状态
4. **异常处理**: 所有方法都包含异常处理，失败时会记录日志
5. **资源清理**: 使用完毕后建议关闭浮窗释放资源

## 依赖关系

- `pages.base.common_page.CommonPage`: 基础页面类
- `pages.apps.ella.ella_response_handler.EllaResponseHandler`: 响应处理器
- `pages.apps.ella.ella_command_executor.EllaCommandExecutor`: 命令执行器
- `pages.apps.ella.ella_multimodal_handler.EllaMultimodalHandler`: 多模态处理器

## 扩展性

该类设计为独立的浮窗页面封装，可以根据需要：

1. **添加新的浮窗元素**: 在 `_init_elements()` 方法中定义
2. **扩展功能方法**: 添加新的浮窗操作方法
3. **自定义响应处理**: 重写响应处理相关方法
4. **集成其他功能**: 与其他Ella功能模块集成
