"""
Ella语音助手浮窗页面
专注于浮窗模式下的页面元素定义和基本页面操作
"""
import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, project_root)

from pages.base.common_page import CommonPage
from pages.base.system_status_checker import SystemStatusChecker
from pages.base.app_detector import AppDetector, AppType
from pages.apps.ella.ella_response_handler import EllaResponseHandler
from pages.apps.ella.ella_command_executor import EllaCommandExecutor
from pages.apps.ella.ella_multimodal_handler import EllaMultimodalHandler
from tools.adb_process_monitor import AdbProcessMonitor
from core.logger import log


class EllaFloatingPage(CommonPage):
    """Ella语音助手浮窗页面"""

    def __init__(self):
        """初始化Ella浮窗页面"""
        super().__init__("ella", "floating_page")

        # 初始化页面元素
        self._init_elements()

        # 初始化功能模块
        self._init_modules()

    def _init_elements(self):
        """初始化浮窗页面元素"""
        # 浮窗主容器
        self.floating_container = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/floating_container"},
            "浮窗主容器"
        )

        # 浮窗输入框
        self.floating_input_box = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/fl_input"},
            "浮窗输入框"
        )

        # 备选浮窗输入框
        self.floating_text_input = self.create_element(
            {"className": "android.widget.EditText"},
            "浮窗文本输入框(备选)"
        )

        # 浮窗发送按钮
        self.floating_send_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/btn_floating_send"},
            "浮窗发送按钮"
        )

        # 浮窗语音输入按钮
        self.floating_voice_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/fl_btn_three_btn"},
            "浮窗语音输入按钮"
        )

        # 浮窗关闭按钮
        self.floating_close_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/btn_floating_close"},
            "浮窗关闭按钮"
        )

        # 浮窗最小化按钮
        self.floating_minimize_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/btn_floating_minimize"},
            "浮窗最小化按钮"
        )

        # 浮窗展开按钮
        self.floating_expand_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/btn_expand"},
            "浮窗展开按钮"
        )

        # 浮窗响应区域
        self.floating_response_area = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/tv_floating_response"},
            "浮窗响应区域"
        )

        # 浮窗聊天列表
        self.floating_chat_list = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/rv_floating_chat"},
            "浮窗聊天列表"
        )

        # 浮窗状态指示器
        self.floating_status_indicator = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/iv_floating_status"},
            "浮窗状态指示器"
        )

        # 浮窗拖拽区域
        self.floating_drag_area = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/drag_area"},
            "浮窗拖拽区域"
        )

        # 通用文本视图（用于获取浮窗内容）
        self.floating_text_view = self.create_element(
            {"className": "android.widget.TextView"},
            "浮窗通用文本视图"
        )

    def _init_modules(self):
        """初始化功能模块"""
        # 页面元素字典，供其他模块使用
        self.page_elements = {
            'floating_input_box': self.floating_input_box,
            'floating_text_input': self.floating_text_input,
            'floating_send_button': self.floating_send_button,
            'floating_voice_button': self.floating_voice_button,
            'floating_close_button': self.floating_close_button,
            'floating_minimize_button': self.floating_minimize_button,
            'floating_expand_button': self.floating_expand_button,
            'floating_response_area': self.floating_response_area,
            'floating_chat_list': self.floating_chat_list,
            'floating_status_indicator': self.floating_status_indicator,
            'floating_drag_area': self.floating_drag_area,
            'floating_text_view': self.floating_text_view
        }

        # 初始化功能模块
        self.status_checker = SystemStatusChecker(self.driver)
        self.app_detector = AppDetector()
        self.process_monitor = AdbProcessMonitor()
        self.response_handler = EllaResponseHandler(self.driver, self.status_checker)
        self.command_executor = EllaCommandExecutor(self.driver, self.page_elements)
        self.multimodal_handler = EllaMultimodalHandler(self.driver, self.page_elements)

    # ==================== 浮窗管理方法 ====================

    def trigger_ella_by_power_key(self, duration: float = 3.0) -> bool:
        """
        通过长按power键唤起Ella浮窗（主要方法）

        Args:
            duration: 长按持续时间（秒），默认3秒

        Returns:
            bool: 是否成功唤起浮窗
        """
        try:
            log.info(f"通过长按power键唤起Ella浮窗（持续{duration}秒）")

            # 确保屏幕是亮着的
            self._ensure_screen_on()

            # 执行长按power键
            if self._long_press_power_key(duration):
                # 等待浮窗出现
                time.sleep(2)

                if self.is_floating_window_visible():
                    log.info("✅ 成功通过长按power键唤起Ella浮窗")
                    return True
                else:
                    log.warning("长按power键执行成功，但浮窗未出现")
                    # 再等待一下，有时候浮窗出现会有延迟
                    time.sleep(3)
                    if self.is_floating_window_visible():
                        log.info("✅ 延迟后检测到Ella浮窗")
                        return True

            log.error("❌ 长按power键未能唤起Ella浮窗")
            return False

        except Exception as e:
            log.error(f"通过长按power键唤起Ella浮窗异常: {e}")
            return False

    def _ensure_screen_on(self) -> bool:
        """
        确保屏幕是亮着的

        Returns:
            bool: 操作是否成功
        """
        try:
            # 检查屏幕状态
            result = self.driver.shell("dumpsys power | grep 'Display Power'")
            if "ON" not in result:
                log.info("屏幕未亮起，尝试点亮屏幕")
                self.driver.press("power")
                time.sleep(1)

                # 如果有锁屏，尝试解锁
                self._try_unlock_screen()

            return True

        except Exception as e:
            log.warning(f"确保屏幕亮起失败: {e}")
            return False

    def _try_unlock_screen(self) -> bool:
        """
        尝试解锁屏幕

        Returns:
            bool: 是否成功
        """
        try:
            # 向上滑动解锁
            screen_size = self.driver.window_size()
            start_x = screen_size['width'] // 2
            start_y = screen_size['height'] * 3 // 4
            end_y = screen_size['height'] // 4

            self.driver.swipe(start_x, start_y, start_x, end_y, 0.5)
            time.sleep(1)

            return True

        except Exception as e:
            log.warning(f"尝试解锁屏幕失败: {e}")
            return False

    def open_floating_window(self) -> bool:
        """打开Ella浮窗"""
        try:
            log.info("尝试打开Ella浮窗")

            # 方法1: 通过长按power键唤起Ella浮窗
            try:
                log.info("方法1: 尝试通过长按power键唤起Ella浮窗")
                # 模拟长按power键 (按住3秒)
                self._long_press_power_key(duration=3.0)
                time.sleep(2)

                if self.is_floating_window_visible():
                    log.info("✅ 通过长按power键成功打开浮窗")
                    return True
            except Exception as e:
                log.warning(f"长按power键打开浮窗失败: {e}")

            # 方法2: 通过shell命令启动浮窗模式
            try:
                log.info("方法2: 尝试通过shell命令启动浮窗")
                cmd = "am start -n com.transsion.aivoiceassistant/.FloatingActivity"
                self.driver.shell(cmd)
                time.sleep(2)

                if self.is_floating_window_visible():
                    log.info("✅ 通过shell命令打开浮窗成功")
                    return True
            except Exception as e:
                log.warning(f"shell命令打开浮窗失败: {e}")

            # 方法3: 通过广播启动浮窗
            try:
                log.info("方法3: 尝试通过广播启动浮窗")
                cmd = "am broadcast -a com.transsion.aivoiceassistant.SHOW_FLOATING_WINDOW"
                self.driver.shell(cmd)
                time.sleep(2)

                if self.is_floating_window_visible():
                    log.info("✅ 通过广播打开浮窗成功")
                    return True
            except Exception as e:
                log.warning(f"广播打开浮窗失败: {e}")

            # 方法4: 通过按键组合唤起
            try:
                log.info("方法4: 尝试通过按键组合唤起浮窗")
                # 尝试其他可能的按键组合
                self._try_key_combinations_for_floating()
                time.sleep(2)

                if self.is_floating_window_visible():
                    log.info("✅ 通过按键组合打开浮窗成功")
                    return True
            except Exception as e:
                log.warning(f"按键组合打开浮窗失败: {e}")

            log.error("❌ 所有方法都无法打开浮窗")
            return False

        except Exception as e:
            log.error(f"打开浮窗异常: {e}")
            return False

    def _long_press_power_key(self, duration: float = 3.0) -> bool:
        """
        模拟长按power键

        Args:
            duration: 长按持续时间（秒）

        Returns:
            bool: 操作是否成功
        """
        try:
            log.info(f"模拟长按power键 {duration}秒")

            # 方法1: 使用adb shell input keyevent长按
            try:
                # 发送按下power键事件
                self.driver.shell("input keyevent --longpress KEYCODE_POWER")
                time.sleep(duration)
                log.info("✅ 长按power键命令执行成功")
                return True
            except Exception as e:
                log.warning(f"longpress命令失败: {e}")

            # 方法2: 使用sendevent模拟长按
            try:
                # 按下power键
                self.driver.shell("sendevent /dev/input/event0 1 116 1")
                self.driver.shell("sendevent /dev/input/event0 0 0 0")
                time.sleep(duration)
                # 释放power键
                self.driver.shell("sendevent /dev/input/event0 1 116 0")
                self.driver.shell("sendevent /dev/input/event0 0 0 0")
                log.info("✅ sendevent长按power键执行成功")
                return True
            except Exception as e:
                log.warning(f"sendevent命令失败: {e}")

            # 方法3: 使用uiautomator的press方法
            try:
                # 连续按power键模拟长按
                for _ in range(int(duration * 2)):  # 每0.5秒按一次
                    self.driver.press("power")
                    time.sleep(0.5)
                log.info("✅ 连续按power键模拟长按成功")
                return True
            except Exception as e:
                log.warning(f"连续按power键失败: {e}")

            return False

        except Exception as e:
            log.error(f"模拟长按power键异常: {e}")
            return False

    def _try_key_combinations_for_floating(self) -> bool:
        """
        尝试其他可能的按键组合来唤起浮窗

        Returns:
            bool: 是否成功
        """
        try:
            log.info("尝试其他按键组合唤起浮窗")

            # 组合1: Power + Volume Up
            try:
                log.debug("尝试 Power + Volume Up")
                self.driver.shell("input keyevent KEYCODE_POWER KEYCODE_VOLUME_UP")
                time.sleep(1)
                if self.is_floating_window_visible():
                    return True
            except Exception as e:
                log.debug(f"Power + Volume Up 失败: {e}")

            # 组合2: Power + Volume Down
            try:
                log.debug("尝试 Power + Volume Down")
                self.driver.shell("input keyevent KEYCODE_POWER KEYCODE_VOLUME_DOWN")
                time.sleep(1)
                if self.is_floating_window_visible():
                    return True
            except Exception as e:
                log.debug(f"Power + Volume Down 失败: {e}")

            # 组合3: 双击Power键
            try:
                log.debug("尝试双击Power键")
                self.driver.press("power")
                time.sleep(0.3)
                self.driver.press("power")
                time.sleep(1)
                if self.is_floating_window_visible():
                    return True
            except Exception as e:
                log.debug(f"双击Power键失败: {e}")

            # 组合4: 长按Home键
            try:
                log.debug("尝试长按Home键")
                self.driver.shell("input keyevent --longpress KEYCODE_HOME")
                time.sleep(1)
                if self.is_floating_window_visible():
                    return True
            except Exception as e:
                log.debug(f"长按Home键失败: {e}")

            return False

        except Exception as e:
            log.error(f"尝试按键组合异常: {e}")
            return False

    def close_floating_window(self) -> bool:
        """关闭Ella浮窗"""
        try:
            log.info("尝试关闭Ella浮窗")
            
            # 方法1: 点击关闭按钮
            if self.floating_close_button.is_exists():
                self.floating_close_button.click()
                time.sleep(1)
                
                if not self.is_floating_window_visible():
                    log.info("✅ 通过关闭按钮成功关闭浮窗")
                    return True

            # 方法2: 通过手势关闭（向上滑动或其他手势）
            if self.floating_container.is_exists():
                # 获取浮窗位置并执行关闭手势
                bounds = self.floating_container.get_bounds()
                if bounds:
                    center_x = (bounds['left'] + bounds['right']) // 2
                    center_y = (bounds['top'] + bounds['bottom']) // 2
                    
                    # 向上滑动关闭
                    self.driver.swipe(center_x, center_y, center_x, center_y - 200, 0.3)
                    time.sleep(1)
                    
                    if not self.is_floating_window_visible():
                        log.info("✅ 通过手势成功关闭浮窗")
                        return True

            # 方法3: 通过广播关闭
            try:
                cmd = "am broadcast -a com.transsion.aivoiceassistant.HIDE_FLOATING_WINDOW"
                self.driver.shell(cmd)
                time.sleep(1)
                
                if not self.is_floating_window_visible():
                    log.info("✅ 通过广播成功关闭浮窗")
                    return True
            except Exception as e:
                log.warning(f"广播关闭浮窗失败: {e}")

            log.error("❌ 无法关闭浮窗")
            return False

        except Exception as e:
            log.error(f"关闭浮窗异常: {e}")
            return False

    def is_floating_window_visible(self) -> bool:
        """检查浮窗是否可见"""
        try:
            # 检查浮窗容器是否存在且可见
            if self.floating_container.is_exists() and self.floating_container.is_displayed():
                return True
            
            # 检查浮窗输入框是否存在
            if self.floating_input_box.is_exists():
                return True
                
            # 检查备选输入框
            if self.floating_text_input.is_exists():
                return True
                
            return False
            
        except Exception as e:
            log.error(f"检查浮窗可见性异常: {e}")
            return False

    def minimize_floating_window(self) -> bool:
        """最小化浮窗"""
        try:
            log.info("尝试最小化浮窗")
            
            if self.floating_minimize_button.is_exists():
                self.floating_minimize_button.click()
                time.sleep(1)
                log.info("✅ 浮窗最小化成功")
                return True
            else:
                log.warning("❌ 未找到最小化按钮")
                return False
                
        except Exception as e:
            log.error(f"最小化浮窗异常: {e}")
            return False

    def expand_floating_window(self) -> bool:
        """展开浮窗"""
        try:
            log.info("尝试展开浮窗")
            
            if self.floating_expand_button.is_exists():
                self.floating_expand_button.click()
                time.sleep(1)
                log.info("✅ 浮窗展开成功")
                return True
            else:
                log.warning("❌ 未找到展开按钮")
                return False
                
        except Exception as e:
            log.error(f"展开浮窗异常: {e}")
            return False

    def move_floating_window(self, target_x: int, target_y: int) -> bool:
        """移动浮窗到指定位置"""
        try:
            log.info(f"尝试移动浮窗到位置: ({target_x}, {target_y})")
            
            if self.floating_drag_area.is_exists():
                # 获取当前拖拽区域的位置
                bounds = self.floating_drag_area.get_bounds()
                if bounds:
                    current_x = (bounds['left'] + bounds['right']) // 2
                    current_y = (bounds['top'] + bounds['bottom']) // 2
                    
                    # 执行拖拽操作
                    self.driver.drag(current_x, current_y, target_x, target_y, 0.5)
                    time.sleep(1)
                    
                    log.info("✅ 浮窗移动完成")
                    return True
            else:
                log.warning("❌ 未找到拖拽区域")
                return False
                
        except Exception as e:
            log.error(f"移动浮窗异常: {e}")
            return False

    # ==================== 命令执行方法 ====================

    def execute_text_command_in_floating(self, command: str) -> bool:
        """在浮窗中执行文本命令"""
        try:
            log.info(f"在浮窗中执行文本命令: {command}")

            if not self.is_floating_window_visible():
                log.error("浮窗不可见，无法执行命令")
                return False

            # 确保输入框可用
            if not self._ensure_floating_input_ready():
                log.error("浮窗输入框不可用")
                return False

            # 清空输入框并输入命令
            input_element = None
            if self.floating_input_box.is_exists():
                input_element = self.floating_input_box
            elif self.floating_text_input.is_exists():
                input_element = self.floating_text_input

            if input_element:
                input_element.clear()
                time.sleep(0.5)
                input_element.send_keys(command)
                time.sleep(0.5)

                # 点击发送按钮
                if self.floating_send_button.is_exists():
                    self.floating_send_button.click()
                    log.info("✅ 浮窗文本命令发送成功")
                    return True
                else:
                    # 尝试按回车键发送
                    self.driver.press("enter")
                    log.info("✅ 浮窗文本命令通过回车发送成功")
                    return True

            log.error("❌ 未找到可用的浮窗输入框")
            return False

        except Exception as e:
            log.error(f"在浮窗中执行文本命令异常: {e}")
            return False

    def execute_voice_command_in_floating(self, command: str, duration: float = 3.0, language: str = "en") -> bool:
        """在浮窗中执行语音命令"""
        try:
            log.info(f"在浮窗中执行语音命令: {command}")

            if not self.is_floating_window_visible():
                log.error("浮窗不可见，无法执行语音命令")
                return False

            if self.floating_voice_button.is_exists():
                self.floating_voice_button.click()
                time.sleep(1)

                # 这里需要集成语音输入功能
                # 暂时使用文本输入作为替代
                log.info(f"语音命令转为文本输入: {command}")
                return self.execute_text_command_in_floating(command)
            else:
                log.error("❌ 未找到浮窗语音按钮")
                return False

        except Exception as e:
            log.error(f"在浮窗中执行语音命令异常: {e}")
            return False

    def _ensure_floating_input_ready(self) -> bool:
        """确保浮窗输入框就绪"""
        try:
            # 检查主输入框
            if self.floating_input_box.wait_for_element(timeout=3):
                if self.floating_input_box.is_enabled():
                    return True

            # 检查备选输入框
            if self.floating_text_input.wait_for_element(timeout=3):
                if self.floating_text_input.is_enabled():
                    return True

            log.warning("浮窗输入框未就绪")
            return False

        except Exception as e:
            log.error(f"检查浮窗输入框就绪状态异常: {e}")
            return False

    # ==================== 响应处理方法 ====================

    def wait_for_floating_response(self, timeout: int = 10) -> bool:
        """等待浮窗AI响应"""
        try:
            log.info(f"等待浮窗AI响应 (超时: {timeout}秒)")

            # 等待响应区域出现内容
            if self.floating_response_area.wait_for_element(timeout=timeout):
                log.info("✅ 浮窗响应已出现")
                return True

            # 备选方案：等待聊天列表更新
            if self.floating_chat_list.wait_for_element(timeout=5):
                log.info("✅ 浮窗聊天列表已更新")
                return True

            log.warning("❌ 浮窗响应等待超时")
            return False

        except Exception as e:
            log.error(f"等待浮窗响应异常: {e}")
            return False

    def get_floating_response_text(self) -> str:
        """获取浮窗响应文本"""
        try:
            # 尝试从响应区域获取
            if self.floating_response_area.is_exists():
                text = self.floating_response_area.get_text()
                if text and text.strip():
                    return text.strip()

            # 尝试从聊天列表获取最新消息
            if self.floating_chat_list.is_exists():
                # 获取聊天列表中的最后一条消息
                text_views = self.driver(className="android.widget.TextView")
                if text_views.exists():
                    for i in range(len(text_views) - 1, -1, -1):
                        text = text_views[i].get_text()
                        if text and text.strip() and not text.strip().startswith("Hi, I'm Ella"):
                            return text.strip()

            # 尝试从通用文本视图获取
            if self.floating_text_view.is_exists():
                text = self.floating_text_view.get_text()
                if text and text.strip():
                    return text.strip()

            log.warning("未获取到浮窗响应文本")
            return ""

        except Exception as e:
            log.error(f"获取浮窗响应文本异常: {e}")
            return ""

    def get_all_floating_response_texts(self) -> list:
        """获取浮窗中所有响应文本"""
        try:
            texts = []

            # 获取所有文本视图的内容
            text_views = self.driver(className="android.widget.TextView")
            if text_views.exists():
                for text_view in text_views:
                    text = text_view.get_text()
                    if text and text.strip():
                        texts.append(text.strip())

            return texts

        except Exception as e:
            log.error(f"获取所有浮窗响应文本异常: {e}")
            return []

    # ==================== 状态检查方法 ====================

    def check_floating_window_status(self) -> dict:
        """检查浮窗状态"""
        try:
            status = {
                'visible': self.is_floating_window_visible(),
                'input_ready': self._ensure_floating_input_ready(),
                'position': None,
                'size': None
            }

            # 获取浮窗位置和大小
            if self.floating_container.is_exists():
                bounds = self.floating_container.get_bounds()
                if bounds:
                    status['position'] = {
                        'x': (bounds['left'] + bounds['right']) // 2,
                        'y': (bounds['top'] + bounds['bottom']) // 2
                    }
                    status['size'] = {
                        'width': bounds['right'] - bounds['left'],
                        'height': bounds['bottom'] - bounds['top']
                    }

            return status

        except Exception as e:
            log.error(f"检查浮窗状态异常: {e}")
            return {'visible': False, 'input_ready': False, 'position': None, 'size': None}

    # ==================== 兼容性方法 ====================

    def ensure_floating_window_ready(self) -> bool:
        """确保浮窗就绪"""
        try:
            log.info("确保浮窗就绪...")

            # 如果浮窗不可见，尝试打开
            if not self.is_floating_window_visible():
                log.info("浮窗不可见，尝试打开")
                if not self.open_floating_window():
                    log.error("无法打开浮窗")
                    return False

            # 等待浮窗完全加载
            time.sleep(2)

            # 检查输入框是否就绪
            if not self._ensure_floating_input_ready():
                log.warning("浮窗输入框未就绪，但浮窗已可见")
                return True  # 宽松策略

            log.info("✅ 浮窗已就绪")
            return True

        except Exception as e:
            log.error(f"确保浮窗就绪异常: {e}")
            return False


if __name__ == '__main__':
    floating_page = EllaFloatingPage()
    status = floating_page.check_floating_window_status()
    print(f"浮窗状态: {status}")
