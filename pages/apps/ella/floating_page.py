"""
Ella语音助手浮窗页面
专注于浮窗模式下的页面元素定义和基本页面操作
"""
import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, project_root)

from pages.base.common_page import CommonPage
from pages.base.system_status_checker import SystemStatusChecker
from pages.base.app_detector import AppDetector, AppType
from pages.apps.ella.ella_response_handler import EllaResponseHandler
from pages.apps.ella.ella_command_executor import EllaCommandExecutor
from pages.apps.ella.ella_multimodal_handler import EllaMultimodalHandler
from tools.adb_process_monitor import AdbProcessMonitor
from core.logger import log


class EllaFloatingPage(CommonPage):
    """Ella语音助手浮窗页面"""

    def __init__(self):
        """初始化Ella浮窗页面"""
        super().__init__("ella", "floating_page")

        # 初始化页面元素
        self._init_elements()

        # 初始化功能模块
        self._init_modules()

    def _init_elements(self):
        """初始化浮窗页面元素 - 基于实际DOM结构"""
        # 浮窗根容器
        self.floating_container = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/fl_root"},
            "浮窗根容器"
        )

        # 语音输入切到键盘输入 (实际定位)
        self.floating_btn_keyboard = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/btn_keyboard"},
            "语音输入切到键盘输入"
        )

        # 浮窗输入框 (实际定位)
        self.floating_input_box = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/et_input"},
            "浮窗输入框"
        )


        # 备选浮窗输入框
        self.floating_text_input = self.create_element(
            {"className": "android.widget.EditText"},
            "浮窗文本输入框(备选)"
        )

        # 输入框容器
        self.floating_input_container = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/fl_input"},
            "浮窗输入框容器"
        )

        # 浮窗语音输入按钮 (实际定位)
        self.floating_voice_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/btn_voice"},
            "浮窗语音输入按钮"
        )

        # 键盘按钮 (用于从语音模式切换到文本模式)
        self.keyboard_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/btn_keyboard"},
            "键盘按钮"
        )

        self.floating_send_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/btn_send"},
            "发送按钮"
        )

        # DeepSeek按钮 (实际定位)
        self.deepseek_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/btn_deep_seek"},
            "DeepSeek按钮"
        )

        # 问候语文本 (实际定位)
        self.greeting_text = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/tv_hello"},
            "问候语文本"
        )

        # 浮窗展开按钮
        self.floating_expand_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/btn_expand"},
            "浮窗展开按钮"
        )

        # 提示文本 (实际定位)
        self.hint_text = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/tv_hint"},
            "输入提示文本"
        )

        # 推荐卡片列表 (实际定位)
        self.recommend_card_list = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/rv_card_recommend_new"},
            "推荐卡片列表"
        )

        # 推荐项目 (实际定位)
        self.recommend_item = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/ll_item_recommend"},
            "推荐项目"
        )

        # 推荐项目文本
        self.recommend_item_text = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/tv_item_recommend"},
            "推荐项目文本"
        )



        # 输入布局容器
        self.input_layout_container = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/rl_input_layout"},
            "输入布局容器"
        )

        # 输入根容器
        self.input_root_container = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/rl_root"},
            "输入根容器"
        )

        # 背景视图
        self.background_view = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/v_bg"},
            "背景视图"
        )

        # 通用文本视图（用于获取浮窗内容）
        self.floating_text_view = self.create_element(
            {"className": "android.widget.TextView"},
            "浮窗通用文本视图"
        )

        # 智能面板浮窗 (右侧小浮窗)
        self.smart_panel_floating = self.create_element(
            {"resourceId": "com.transsion.smartpanel:id/floating_view"},
            "智能面板浮窗"
        )

    def _init_modules(self):
        """初始化功能模块"""
        # 页面元素字典，供其他模块使用 (更新为实际DOM结构)
        self.page_elements = {
            'floating_container': self.floating_container,
            'floating_input_box': self.floating_input_box,
            'floating_text_input': self.floating_text_input,
            'floating_input_container': self.floating_input_container,
            'floating_voice_button': self.floating_voice_button,
            'floating_send_button': self.floating_send_button,
            'keyboard_button': self.keyboard_button,
            'floating_expand_button': self.floating_expand_button,
            'deepseek_button': self.deepseek_button,
            'greeting_text': self.greeting_text,
            'hint_text': self.hint_text,
            'recommend_card_list': self.recommend_card_list,
            'recommend_item': self.recommend_item,
            'recommend_item_text': self.recommend_item_text,
            'input_layout_container': self.input_layout_container,
            'input_root_container': self.input_root_container,
            'background_view': self.background_view,
            'floating_text_view': self.floating_text_view,
            'smart_panel_floating': self.smart_panel_floating
        }

        # 初始化功能模块
        self.status_checker = SystemStatusChecker(self.driver)
        self.app_detector = AppDetector()
        self.process_monitor = AdbProcessMonitor()
        self.response_handler = EllaResponseHandler(self.driver, self.status_checker)
        self.command_executor = EllaCommandExecutor(self.driver, self.page_elements)
        self.multimodal_handler = EllaMultimodalHandler(self.driver, self.page_elements)

    # ==================== 浮窗管理方法 ====================

    def trigger_ella_by_power_key(self, duration: float = 3.0) -> bool:
        """
        通过长按power键唤起Ella浮窗（主要方法）

        Args:
            duration: 长按持续时间（秒），默认3秒

        Returns:
            bool: 是否成功唤起浮窗
        """
        try:
            log.info(f"通过长按power键唤起Ella浮窗（持续{duration}秒）")

            # 确保屏幕是亮着的
            self._ensure_screen_on()

            # 执行长按power键
            if self._long_press_power_key(duration):
                # 等待浮窗出现
                time.sleep(2)

                if self.is_floating_window_visible():
                    log.info("✅ 成功通过长按power键唤起Ella浮窗")
                    return True
                else:
                    log.warning("长按power键执行成功，但浮窗未出现")
                    # 再等待一下，有时候浮窗出现会有延迟
                    time.sleep(3)
                    if self.is_floating_window_visible():
                        log.info("✅ 延迟后检测到Ella浮窗")
                        return True

            log.error("❌ 长按power键未能唤起Ella浮窗")
            return False

        except Exception as e:
            log.error(f"通过长按power键唤起Ella浮窗异常: {e}")
            return False

    def _ensure_screen_on(self) -> bool:
        """
        确保屏幕是亮着的

        Returns:
            bool: 操作是否成功
        """
        try:
            # 检查屏幕状态
            result = self.driver.shell("dumpsys power | grep 'Display Power'")
            if "ON" not in result:
                log.info("屏幕未亮起，尝试点亮屏幕")
                self.driver.press("power")
                time.sleep(1)

                # 如果有锁屏，尝试解锁
                self._try_unlock_screen()

            return True

        except Exception as e:
            log.warning(f"确保屏幕亮起失败: {e}")
            return False

    def _try_unlock_screen(self) -> bool:
        """
        尝试解锁屏幕

        Returns:
            bool: 是否成功
        """
        try:
            # 向上滑动解锁
            screen_size = self.driver.window_size()

            # window_size() 返回元组 (width, height)
            if isinstance(screen_size, tuple) and len(screen_size) >= 2:
                width, height = screen_size[0], screen_size[1]
            elif isinstance(screen_size, dict):
                width, height = screen_size['width'], screen_size['height']
            else:
                log.warning(f"无法解析屏幕尺寸: {screen_size}")
                return False

            start_x = width // 2
            start_y = height * 3 // 4
            end_y = height // 4

            self.driver.swipe(start_x, start_y, start_x, end_y, 0.5)
            time.sleep(1)

            return True

        except Exception as e:
            log.warning(f"尝试解锁屏幕失败: {e}")
            return False

    def open_floating_window(self) -> bool:
        """打开Ella浮窗"""
        try:
            log.info("尝试打开Ella浮窗")

            # 方法1: 通过长按power键唤起Ella浮窗
            try:
                log.info("方法1: 尝试通过长按power键唤起Ella浮窗")
                # 模拟长按power键 (按住3秒)
                self._long_press_power_key(duration=3.0)
                time.sleep(2)

                if self.is_floating_window_visible():
                    log.info("✅ 通过长按power键成功打开浮窗")
                    return True
            except Exception as e:
                log.warning(f"长按power键打开浮窗失败: {e}")

            # 方法2: 通过shell命令启动浮窗模式
            try:
                log.info("方法2: 尝试通过shell命令启动浮窗")
                cmd = "am start -n com.transsion.aivoiceassistant/.FloatingActivity"
                self.driver.shell(cmd)
                time.sleep(2)

                if self.is_floating_window_visible():
                    log.info("✅ 通过shell命令打开浮窗成功")
                    return True
            except Exception as e:
                log.warning(f"shell命令打开浮窗失败: {e}")

            # 方法3: 通过广播启动浮窗
            try:
                log.info("方法3: 尝试通过广播启动浮窗")
                cmd = "am broadcast -a com.transsion.aivoiceassistant.SHOW_FLOATING_WINDOW"
                self.driver.shell(cmd)
                time.sleep(2)

                if self.is_floating_window_visible():
                    log.info("✅ 通过广播打开浮窗成功")
                    return True
            except Exception as e:
                log.warning(f"广播打开浮窗失败: {e}")

            # 方法4: 通过按键组合唤起
            try:
                log.info("方法4: 尝试通过按键组合唤起浮窗")
                # 尝试其他可能的按键组合
                self._try_key_combinations_for_floating()
                time.sleep(2)

                if self.is_floating_window_visible():
                    log.info("✅ 通过按键组合打开浮窗成功")
                    return True
            except Exception as e:
                log.warning(f"按键组合打开浮窗失败: {e}")

            log.error("❌ 所有方法都无法打开浮窗")
            return False

        except Exception as e:
            log.error(f"打开浮窗异常: {e}")
            return False

    def _long_press_power_key(self, duration: float = 3.0) -> bool:
        """
        模拟长按power键

        Args:
            duration: 长按持续时间（秒）

        Returns:
            bool: 操作是否成功
        """
        try:
            log.info(f"模拟长按power键 {duration}秒")

            # 方法1: 使用adb shell input keyevent长按
            try:
                # 发送长按power键事件（--longpress已经包含了长按逻辑）
                self.driver.shell("input keyevent --longpress KEYCODE_POWER")
                log.info("✅ 长按power键命令执行成功")
                return True
            except Exception as e:
                log.warning(f"longpress命令失败: {e}")

            # 方法2: 使用sendevent模拟长按
            try:
                # 按下power键
                self.driver.shell("sendevent /dev/input/event0 1 116 1")
                self.driver.shell("sendevent /dev/input/event0 0 0 0")
                time.sleep(duration)
                # 释放power键
                self.driver.shell("sendevent /dev/input/event0 1 116 0")
                self.driver.shell("sendevent /dev/input/event0 0 0 0")
                log.info("✅ sendevent长按power键执行成功")
                return True
            except Exception as e:
                log.warning(f"sendevent命令失败: {e}")

            # 方法3: 使用uiautomator的press方法
            try:
                # 连续按power键模拟长按
                for _ in range(int(duration * 2)):  # 每0.5秒按一次
                    self.driver.press("power")
                    time.sleep(0.5)
                log.info("✅ 连续按power键模拟长按成功")
                return True
            except Exception as e:
                log.warning(f"连续按power键失败: {e}")

            return False

        except Exception as e:
            log.error(f"模拟长按power键异常: {e}")
            return False

    def _try_key_combinations_for_floating(self) -> bool:
        """
        尝试其他可能的按键组合来唤起浮窗

        Returns:
            bool: 是否成功
        """
        try:
            log.info("尝试其他按键组合唤起浮窗")

            # 组合1: Power + Volume Up
            try:
                log.debug("尝试 Power + Volume Up")
                self.driver.shell("input keyevent KEYCODE_POWER KEYCODE_VOLUME_UP")
                time.sleep(1)
                if self.is_floating_window_visible():
                    return True
            except Exception as e:
                log.debug(f"Power + Volume Up 失败: {e}")

            # 组合2: Power + Volume Down
            try:
                log.debug("尝试 Power + Volume Down")
                self.driver.shell("input keyevent KEYCODE_POWER KEYCODE_VOLUME_DOWN")
                time.sleep(1)
                if self.is_floating_window_visible():
                    return True
            except Exception as e:
                log.debug(f"Power + Volume Down 失败: {e}")

            # 组合3: 双击Power键
            try:
                log.debug("尝试双击Power键")
                self.driver.press("power")
                time.sleep(0.3)
                self.driver.press("power")
                time.sleep(1)
                if self.is_floating_window_visible():
                    return True
            except Exception as e:
                log.debug(f"双击Power键失败: {e}")

            # 组合4: 长按Home键
            try:
                log.debug("尝试长按Home键")
                self.driver.shell("input keyevent --longpress KEYCODE_HOME")
                time.sleep(1)
                if self.is_floating_window_visible():
                    return True
            except Exception as e:
                log.debug(f"长按Home键失败: {e}")

            return False

        except Exception as e:
            log.error(f"尝试按键组合异常: {e}")
            return False

    def close_floating_window(self) -> bool:
        """关闭Ella浮窗"""
        try:
            log.info("尝试关闭Ella浮窗")
            
            # 方法1: 点击关闭按钮
            if self.floating_close_button.is_exists():
                self.floating_close_button.click()
                time.sleep(1)
                
                if not self.is_floating_window_visible():
                    log.info("✅ 通过关闭按钮成功关闭浮窗")
                    return True

            # 方法2: 通过手势关闭（向上滑动或其他手势）
            if self.floating_container.is_exists():
                # 获取浮窗位置并执行关闭手势
                bounds = self.floating_container.get_bounds()
                if bounds:
                    center_x = (bounds['left'] + bounds['right']) // 2
                    center_y = (bounds['top'] + bounds['bottom']) // 2
                    
                    # 向上滑动关闭
                    self.driver.swipe(center_x, center_y, center_x, center_y - 200, 0.3)
                    time.sleep(1)
                    
                    if not self.is_floating_window_visible():
                        log.info("✅ 通过手势成功关闭浮窗")
                        return True

            # 方法3: 通过广播关闭
            try:
                cmd = "am broadcast -a com.transsion.aivoiceassistant.HIDE_FLOATING_WINDOW"
                self.driver.shell(cmd)
                time.sleep(1)
                
                if not self.is_floating_window_visible():
                    log.info("✅ 通过广播成功关闭浮窗")
                    return True
            except Exception as e:
                log.warning(f"广播关闭浮窗失败: {e}")

            log.error("❌ 无法关闭浮窗")
            return False

        except Exception as e:
            log.error(f"关闭浮窗异常: {e}")
            return False

    def is_floating_window_visible(self) -> bool:
        """检查浮窗是否可见 - 基于实际DOM结构"""
        try:
            # 方法1: 检查浮窗根容器是否存在
            if self.floating_container.is_exists():
                log.debug("通过浮窗根容器检测到浮窗可见")
                return True

            # 方法2: 检查输入框是否存在
            if self.floating_input_box.is_exists():
                log.debug("通过输入框检测到浮窗可见")
                return True

            # 方法3: 检查问候语文本是否存在
            if self.greeting_text.is_exists():
                log.debug("通过问候语文本检测到浮窗可见")
                return True

            # 方法4: 检查DeepSeek按钮是否存在
            if self.deepseek_button.is_exists():
                log.debug("通过DeepSeek按钮检测到浮窗可见")
                return True

            # 方法5: 通过包名检查
            try:
                current_app = self.driver.app_current()
                if current_app.get('package') == 'com.transsion.aivoiceassistant':
                    log.debug("通过应用包名检测到Ella浮窗")
                    return True
            except Exception:
                pass

            return False

        except Exception as e:
            log.error(f"检查浮窗可见性异常: {e}")
            return False

    def minimize_floating_window(self) -> bool:
        """最小化浮窗"""
        try:
            log.info("尝试最小化浮窗")
            
            if self.floating_minimize_button.is_exists():
                self.floating_minimize_button.click()
                time.sleep(1)
                log.info("✅ 浮窗最小化成功")
                return True
            else:
                log.warning("❌ 未找到最小化按钮")
                return False
                
        except Exception as e:
            log.error(f"最小化浮窗异常: {e}")
            return False

    def expand_floating_window(self) -> bool:
        """展开浮窗"""
        try:
            log.info("尝试展开浮窗")
            
            if self.floating_expand_button.is_exists():
                self.floating_expand_button.click()
                time.sleep(1)
                log.info("✅ 浮窗展开成功")
                return True
            else:
                log.warning("❌ 未找到展开按钮")
                return False
                
        except Exception as e:
            log.error(f"展开浮窗异常: {e}")
            return False

    def move_floating_window(self, target_x: int, target_y: int) -> bool:
        """移动浮窗到指定位置"""
        try:
            log.info(f"尝试移动浮窗到位置: ({target_x}, {target_y})")
            
            if self.floating_drag_area.is_exists():
                # 获取当前拖拽区域的位置
                bounds = self.floating_drag_area.get_bounds()
                if bounds:
                    current_x = (bounds['left'] + bounds['right']) // 2
                    current_y = (bounds['top'] + bounds['bottom']) // 2
                    
                    # 执行拖拽操作
                    self.driver.drag(current_x, current_y, target_x, target_y, 0.5)
                    time.sleep(1)
                    
                    log.info("✅ 浮窗移动完成")
                    return True
            else:
                log.warning("❌ 未找到拖拽区域")
                return False
                
        except Exception as e:
            log.error(f"移动浮窗异常: {e}")
            return False

    # ==================== 命令执行方法 ====================

    def _ensure_text_input_mode(self) -> bool:
        """
        确保当前处于文本输入模式

        Returns:
            bool: 是否成功切换到文本输入模式
        """
        try:
            log.debug("检查当前输入模式...")

            # 检查是否存在键盘按钮
            if self.keyboard_button.is_exists():
                log.info("检测到键盘按钮，当前处于语音模式，需要切换到文本模式")

                # 点击键盘按钮切换到文本输入模式
                self.keyboard_button.click()
                time.sleep(1)  # 等待模式切换完成

                # 再次检查键盘按钮是否消失
                if not self.keyboard_button.is_exists():
                    log.info("✅ 成功切换到文本输入模式")
                    return True
                else:
                    log.warning("⚠️ 键盘按钮仍然存在，可能切换失败")
                    # 尝试再次点击
                    self.keyboard_button.click()
                    time.sleep(1)

                    if not self.keyboard_button.is_exists():
                        log.info("✅ 第二次尝试成功切换到文本输入模式")
                        return True
                    else:
                        log.error("❌ 无法切换到文本输入模式")
                        return False
            else:
                log.debug("未检测到键盘按钮，当前已处于文本输入模式")
                return True

        except Exception as e:
            log.error(f"确保文本输入模式异常: {e}")
            return False

    def _check_current_input_mode(self) -> str:
        """
        检查当前输入模式

        Returns:
            str: 'voice' 表示语音模式, 'text' 表示文本模式, 'unknown' 表示未知
        """
        try:
            if self.keyboard_button.is_exists():
                return 'voice'
            elif self.floating_voice_button.is_exists():
                return 'text'
            else:
                return 'unknown'
        except Exception as e:
            log.error(f"检查输入模式异常: {e}")
            return 'unknown'

    def execute_text_command_in_floating(self, command: str) -> bool:
        """
        在浮窗中执行文本命令

        Args:
            command: 要执行的文本命令

        Returns:
            bool: 是否成功执行命令
        """
        try:
            log.info(f"在浮窗中执行文本命令: {command}")

            if not self.is_floating_window_visible():
                log.error("浮窗不可见，无法执行命令")
                return False

            # 1. 检查当前输入模式
            current_mode = self._check_current_input_mode()
            log.info(f"当前输入模式: {current_mode}")

            # 2. 确保切换到文本输入模式
            if not self._ensure_text_input_mode():
                log.error("无法切换到文本输入模式")
                return False

            # 3. 确保输入框可用
            if not self._ensure_floating_input_ready():
                log.error("浮窗输入框不可用")
                return False

            # 4. 查找可用的输入框
            input_element = None
            if self.floating_input_box.is_exists():
                input_element = self.floating_input_box
                log.debug("使用主输入框")
            elif self.floating_text_input.is_exists():
                input_element = self.floating_text_input
                log.debug("使用备选输入框")

            if not input_element:
                log.error("❌ 未找到可用的浮窗输入框")
                return False

            # 5. 清空输入框并输入命令
            try:
                # 清空输入框
                if not input_element.clear_text():
                    log.warning("清空输入框失败，尝试继续输入")
                time.sleep(0.5)

                # 输入命令
                log.debug(f"输入命令: {command}")
                if not input_element.send_keys(command):
                    log.error("输入命令失败")
                    return False
                time.sleep(0.5)
            except Exception as e:
                log.error(f"输入命令异常: {e}")
                return False

            # 6. 发送命令 - 优化版本（优先使用发送按钮）

            # 方法1: 优先查找并点击发送按钮
            send_buttons = [
                ("语音按钮", self.floating_voice_button),  # 在文本模式下，语音按钮通常作为发送按钮
                ("展开按钮", self.floating_expand_button),  # 有时展开按钮也可以触发发送
                ("DeepSeek按钮", self.deepseek_button),     # DeepSeek按钮可能也有发送功能
            ]

            for button_name, button_element in send_buttons:
                try:
                    if button_element.is_exists():
                        log.debug(f"尝试通过{button_name}发送命令")
                        button_element.click()
                        time.sleep(0.5)  # 等待发送处理
                        log.info(f"✅ 通过{button_name}发送命令成功")
                        return True
                except Exception as e:
                    log.debug(f"通过{button_name}发送失败: {e}")

            # 方法2: 尝试按回车键发送（备选方案）
            try:
                log.debug("尝试通过回车键发送命令")
                self.driver.press("enter")
                time.sleep(0.5)  # 等待发送处理
                log.info("✅ 浮窗文本命令通过回车发送成功")
                return True
            except Exception as e:
                log.warning(f"回车发送失败: {e}")

            # 方法3: 尝试点击输入框右侧区域触发发送
            try:
                log.debug("尝试通过点击输入框右侧区域发送命令")
                bounds = input_element.get_bounds()
                if bounds and len(bounds) >= 4:
                    x = bounds[2] + 50  # 输入框右边50像素
                    y = (bounds[1] + bounds[3]) // 2  # 输入框中心Y坐标
                    self.driver.click(x, y)
                    time.sleep(0.5)  # 等待发送处理
                    log.info("✅ 通过点击输入框右侧区域发送命令成功")
                    return True
            except Exception as e:
                log.debug(f"点击输入框右侧区域发送失败: {e}")

            # 方法4: 尝试点击输入框本身触发发送
            try:
                log.debug("尝试通过点击输入框本身发送命令")
                input_element.click()
                time.sleep(0.5)
                log.info("✅ 通过点击输入框发送命令成功")
                return True
            except Exception as e:
                log.debug(f"点击输入框发送失败: {e}")

            log.error("❌ 所有发送方法都失败了")
            return False

        except Exception as e:
            log.error(f"在浮窗中执行文本命令异常: {e}")
            return False

    def execute_voice_command_in_floating(self, command: str, duration: float = 3.0, language: str = "en") -> bool:
        """在浮窗中执行语音命令"""
        try:
            log.info(f"在浮窗中执行语音命令: {command}")

            if not self.is_floating_window_visible():
                log.error("浮窗不可见，无法执行语音命令")
                return False

            if self.floating_voice_button.is_exists():
                self.floating_voice_button.click()
                time.sleep(1)

                # 这里需要集成语音输入功能
                # 暂时使用文本输入作为替代
                log.info(f"语音命令转为文本输入: {command}")
                return self.execute_text_command_in_floating(command)
            else:
                log.error("❌ 未找到浮窗语音按钮")
                return False

        except Exception as e:
            log.error(f"在浮窗中执行语音命令异常: {e}")
            return False

    def _ensure_floating_input_ready(self) -> bool:
        """确保浮窗输入框就绪 - 基于实际DOM结构"""
        try:
            # 检查主输入框
            if self.floating_input_box.wait_for_element(timeout=3):
                if self.floating_input_box.is_enabled():
                    log.debug("主输入框已就绪")
                    return True

            # 检查输入框容器
            if self.floating_input_container.wait_for_element(timeout=2):
                log.debug("输入框容器已就绪")
                return True

            # 检查提示文本是否存在（说明输入区域已加载）
            if self.hint_text.is_exists():
                log.debug("通过提示文本检测到输入区域已就绪")
                return True

            # 检查备选输入框
            if self.floating_text_input.wait_for_element(timeout=2):
                if self.floating_text_input.is_enabled():
                    log.debug("备选输入框已就绪")
                    return True

            log.warning("浮窗输入框未就绪")
            return False

        except Exception as e:
            log.error(f"检查浮窗输入框就绪状态异常: {e}")
            return False

    # ==================== 响应处理方法 ====================

    def wait_for_floating_response(self, timeout: int = 10) -> bool:
        """等待浮窗AI响应"""
        try:
            log.info(f"等待浮窗AI响应 (超时: {timeout}秒)")

            # 等待响应区域出现内容
            if self.floating_response_area.wait_for_element(timeout=timeout):
                log.info("✅ 浮窗响应已出现")
                return True

            # 备选方案：等待聊天列表更新
            if self.floating_chat_list.wait_for_element(timeout=5):
                log.info("✅ 浮窗聊天列表已更新")
                return True

            log.warning("❌ 浮窗响应等待超时")
            return False

        except Exception as e:
            log.error(f"等待浮窗响应异常: {e}")
            return False

    def get_floating_response_text(self) -> str:
        """获取浮窗响应文本 - 基于实际DOM结构"""
        try:
            # 方法1: 从问候语获取（如果是初始状态）
            if self.greeting_text.is_exists():
                text = self.greeting_text.get_text()
                if text and text.strip() and text.strip() != "Good morning!":
                    log.debug(f"从问候语获取到响应: {text.strip()}")
                    return text.strip()

            # 方法2: 从推荐项目文本获取
            if self.recommend_item_text.is_exists():
                text = self.recommend_item_text.get_text()
                if text and text.strip():
                    log.debug(f"从推荐项目获取到文本: {text.strip()}")
                    return text.strip()

            # 方法3: 从所有文本视图中查找AI响应
            text_views = self.driver(className="android.widget.TextView")
            if text_views.exists():
                for i in range(len(text_views) - 1, -1, -1):
                    try:
                        text = text_views[i].get_text()
                        if text and text.strip():
                            # 过滤掉系统文本和固定文本
                            filtered_texts = [
                                "Good morning!", "Feel free to ask me any questions…",
                                "DeepSeek-R1", "AI Wallpaper Generator", "Vogue Portraits"
                            ]
                            if text.strip() not in filtered_texts and len(text.strip()) > 10:
                                log.debug(f"从文本视图获取到响应: {text.strip()}")
                                return text.strip()
                    except Exception:
                        continue

            # 方法4: 从通用文本视图获取
            if self.floating_text_view.is_exists():
                text = self.floating_text_view.get_text()
                if text and text.strip():
                    log.debug(f"从通用文本视图获取到响应: {text.strip()}")
                    return text.strip()

            log.warning("未获取到浮窗响应文本")
            return ""

        except Exception as e:
            log.error(f"获取浮窗响应文本异常: {e}")
            return ""

    def get_all_floating_response_texts(self) -> list:
        """获取浮窗中所有响应文本"""
        try:
            texts = []

            # 获取所有文本视图的内容
            text_views = self.driver(className="android.widget.TextView")
            if text_views.exists():
                for text_view in text_views:
                    text = text_view.get_text()
                    if text and text.strip():
                        texts.append(text.strip())

            return texts

        except Exception as e:
            log.error(f"获取所有浮窗响应文本异常: {e}")
            return []

    # ==================== 状态检查方法 ====================

    def check_floating_window_status(self) -> dict:
        """检查浮窗状态"""
        try:
            status = {
                'visible': self.is_floating_window_visible(),
                'input_ready': self._ensure_floating_input_ready(),
                'position': None,
                'size': None
            }

            # 获取浮窗位置和大小
            if self.floating_container.is_exists():
                bounds = self.floating_container.get_bounds()
                if bounds:
                    # bounds 是元组格式: (left, top, right, bottom)
                    if isinstance(bounds, tuple) and len(bounds) >= 4:
                        left, top, right, bottom = bounds[0], bounds[1], bounds[2], bounds[3]
                        status['position'] = {
                            'x': (left + right) // 2,
                            'y': (top + bottom) // 2
                        }
                        status['size'] = {
                            'width': right - left,
                            'height': bottom - top
                        }
                    elif isinstance(bounds, dict):
                        # 如果是字典格式
                        status['position'] = {
                            'x': (bounds['left'] + bounds['right']) // 2,
                            'y': (bounds['top'] + bounds['bottom']) // 2
                        }
                        status['size'] = {
                            'width': bounds['right'] - bounds['left'],
                            'height': bounds['bottom'] - bounds['top']
                        }

            return status

        except Exception as e:
            log.error(f"检查浮窗状态异常: {e}")
            return {'visible': False, 'input_ready': False, 'position': None, 'size': None}

    # ==================== 兼容性方法 ====================

    def ensure_floating_window_ready(self) -> bool:
        """确保浮窗就绪"""
        try:
            log.info("确保浮窗就绪...")

            # 如果浮窗不可见，尝试打开
            if not self.is_floating_window_visible():
                log.info("浮窗不可见，尝试打开")
                if not self.open_floating_window():
                    log.error("无法打开浮窗")
                    return False

            # 等待浮窗完全加载
            time.sleep(2)

            # 检查输入框是否就绪
            if not self._ensure_floating_input_ready():
                log.warning("浮窗输入框未就绪，但浮窗已可见")
                return True  # 宽松策略

            log.info("✅ 浮窗已就绪")
            return True

        except Exception as e:
            log.error(f"确保浮窗就绪异常: {e}")
            return False


if __name__ == '__main__':
    floating_page = EllaFloatingPage()
    # floating_page.trigger_ella_by_power_key(duration=3.0)
    floating_page.open_floating_window()
    floating_page.execute_text_command_in_floating(command="Hello Ella")
    # status = floating_page.check_floating_window_status()
    # print(f"浮窗状态: {status}")
